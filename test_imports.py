#!/usr/bin/env python3
"""
Test script to identify import issues in the DCST Tool.
"""

import sys
import os

print("Testing imports step by step...")

# Test basic imports
try:
    import time
    print("✓ time module imported")
except Exception as e:
    print(f"✗ time module failed: {e}")

try:
    import psutil
    print("✓ psutil imported")
except Exception as e:
    print(f"✗ psutil failed: {e}")

try:
    import networkx as nx
    print("✓ networkx imported")
except Exception as e:
    print(f"✗ networkx failed: {e}")

try:
    import numpy as np
    print("✓ numpy imported")
except Exception as e:
    print(f"✗ numpy failed: {e}")

# Test optional GPU imports
try:
    import torch
    print("✓ PyTorch imported")
    cuda_available = torch.cuda.is_available()
    print(f"  CUDA available: {cuda_available}")
except Exception as e:
    print(f"ℹ PyTorch not available: {e}")

try:
    import cupy as cp
    print("✓ CuPy imported")
except Exception as e:
    print(f"ℹ CuPy not available: {e}")

# Test algorithms module import
print("\nTesting algorithms module import...")
try:
    sys.path.insert(0, 'app')
    print("Added app to path")
    
    # Import step by step
    print("Importing algorithms module...")
    import algorithms
    print("✓ algorithms module imported successfully")
    
    # Test specific functions
    print("Testing specific functions...")
    
    cpu_cores, total_ram, available_ram = algorithms.detect_system_resources()
    print(f"✓ detect_system_resources: {cpu_cores} cores, {total_ram:.1f}GB RAM")
    
    optimal_workers = algorithms.calculate_optimal_workers()
    print(f"✓ calculate_optimal_workers: {optimal_workers}")
    
    cpu_usage = algorithms.monitor_cpu_usage()
    print(f"✓ monitor_cpu_usage: {cpu_usage:.1f}%")
    
    print("✓ All algorithms functions working")
    
except Exception as e:
    print(f"✗ algorithms module failed: {e}")
    import traceback
    traceback.print_exc()

print("\nImport test completed.")
