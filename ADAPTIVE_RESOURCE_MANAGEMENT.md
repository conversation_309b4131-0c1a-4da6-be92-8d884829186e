# Adaptive Resource Management and GPU Acceleration

## Overview

The DCST Tool now includes advanced adaptive resource management and optional GPU acceleration to prevent system crashes and improve performance on resource-constrained machines.

## 🚀 New Features

### 1. Adaptive Parallelization Based on System Resources

The system automatically detects available CPU cores and RAM, then calculates optimal worker counts with configurable safety margins.

**Key Benefits:**
- Prevents system overload and crashes
- Automatically adapts to different hardware configurations
- Maintains system responsiveness during computation

**How it works:**
```python
# Automatic resource detection
cpu_cores, total_ram_gb, available_ram_gb = detect_system_resources()

# Optimal worker calculation with safety margin
optimal_workers = calculate_optimal_workers(safety_margin=0.7)

# Formula: min(cpu_cores * safety_margin, available_ram_gb * safety_margin / min_ram_per_worker)
```

### 2. Real-time CPU Throttling

Monitors CPU usage during execution and automatically reduces worker count when usage exceeds 90%.

**Features:**
- Real-time CPU monitoring using psutil
- Automatic worker reduction when CPU > 90%
- Gradual worker increase when CPU < 54%
- Prevents system freezing and crashes

### 3. Optional GPU Acceleration

Supports both NVIDIA CUDA and AMD ROCm hardware with graceful fallback to CPU.

**Supported Libraries:**
- **PyTorch**: For NVIDIA CUDA and AMD ROCm
- **CuPy**: Drop-in NumPy replacement for GPU operations

**Installation:**
```bash
# For NVIDIA CUDA
pip install torch cupy-cuda12x

# For AMD ROCm  
pip install torch cupy-rocm-5-0
```

### 4. Enhanced Algorithm Integration

All parallelization points now use adaptive resource management:

- `parallel_cost_evaluation()`: Cost calculations with GPU support
- `parallel_edge_swap_evaluation()`: Edge swap operations
- `parallel_local_search()`: Multi-threaded local search
- Simulated Annealing neighbor generation

## 📊 Performance Characteristics

### Graph Size Recommendations

| Graph Size | Strategy | Workers | GPU | Notes |
|------------|----------|---------|-----|-------|
| < 30 nodes | Sequential | 1 | No | Fastest for small graphs |
| 30-100 nodes | Parallel | Adaptive | Optional | Balanced performance |
| 100-500 nodes | Parallel | Conservative | Yes | Optimal for medium graphs |
| > 500 nodes | Conservative | Limited | Yes | Prevents system overload |

### Safety Margins

| Margin | Use Case | Workers | RAM Usage |
|--------|----------|---------|-----------|
| 50% | Conservative | Low | Minimal |
| 70% | Default | Balanced | Moderate |
| 90% | Aggressive | High | Maximum |

## 🔧 Configuration

### Default Settings

```python
# Resource detection
_resource_safety_margin = 0.7  # 70% safety margin
_cpu_check_interval = 1.0      # Check CPU every 1 second
_cpu_threshold = 90.0          # Throttle when CPU > 90%

# Parallelization thresholds
min_nodes_for_parallel = 30    # Minimum nodes for parallelization
min_candidates_for_parallel = 3  # Minimum candidates for parallel evaluation
```

### Customization

You can adjust these parameters by modifying the global variables in `algorithms.py`:

```python
# More conservative settings
_resource_safety_margin = 0.5
_cpu_threshold = 80.0

# More aggressive settings  
_resource_safety_margin = 0.9
_cpu_threshold = 95.0
```

## 🛡️ System Protection Features

### 1. Graceful Degradation
- Automatic fallback to sequential execution when parallel fails
- Timeout protection (20-30 seconds) for parallel operations
- Exception handling with detailed logging

### 2. Memory Management
- Reserves 5GB+ for OS and other applications
- Calculates RAM per worker to prevent memory exhaustion
- Automatic garbage collection in memory-intensive operations

### 3. Process Pool Protection
- Handles "paging file too small" errors gracefully
- Automatic process cleanup on failures
- Comprehensive error logging and recovery

## 📈 Monitoring and Logging

### Resource Monitoring
```
INFO: System resources detected: 16 CPU cores, 31.9GB total RAM, 26.1GB available RAM
INFO: Optimal workers calculated: 11 (CPU-based: 11, RAM-based: 52, safety margin: 70.0%)
```

### CPU Throttling
```
WARNING: High CPU usage detected (100.0%). Reducing workers from 11 to 10
WARNING: Very high CPU usage detected (100.0%) during parallel evaluation
```

### GPU Status
```
INFO: GPU acceleration not available, using CPU-only mode
INFO: GPU acceleration available: NVIDIA_CUDA
INFO: GPU devices: 1, Current: NVIDIA GeForce RTX 3080
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Basic functionality test
python test_adaptive_resources.py

# Feature demonstration (safe)
python demo_adaptive_features.py
```

## 🔍 Troubleshooting

### Common Issues

1. **"Paging file too small" errors**
   - Solution: Automatic fallback to sequential execution
   - The system detects this and reduces parallelization

2. **High CPU usage warnings**
   - Solution: Automatic worker reduction
   - Normal behavior for resource-constrained systems

3. **GPU not detected**
   - Install PyTorch: `pip install torch`
   - Install CuPy for your GPU type
   - Restart the application

### Performance Tips

1. **For large graphs (>1000 nodes):**
   - Enable GPU acceleration if available
   - Use conservative safety margins (50-60%)
   - Monitor system resources during execution

2. **For resource-constrained systems:**
   - Reduce safety margin to 50%
   - Lower CPU threshold to 80%
   - Use sequential execution for graphs <100 nodes

## 🔄 Migration from Previous Version

The new adaptive features are **backward compatible**. Existing code will work without changes, but will now benefit from:

- Automatic resource management
- System crash prevention  
- Better performance on multi-core systems
- Optional GPU acceleration

No code changes required - the improvements are automatic!

## 📝 Technical Implementation

### Key Functions Added

- `detect_system_resources()`: System capability detection
- `calculate_optimal_workers()`: Adaptive worker calculation
- `monitor_cpu_usage()`: Real-time CPU monitoring
- `adaptive_worker_adjustment()`: Dynamic worker scaling
- `calculate_cost_gpu_accelerated()`: GPU-accelerated cost calculation

### Modified Functions

- `parallel_cost_evaluation()`: Added adaptive resource management
- `parallel_edge_swap_evaluation()`: Enhanced with CPU monitoring
- `parallel_local_search()`: Improved resource allocation
- `simulated_annealing_spanning_tree()`: Adaptive neighbor generation

All changes maintain full backward compatibility while adding the new adaptive features.
