#!/usr/bin/env python3
"""
Test script to verify the Local Search edge error fixes and performance optimizations.
"""

import os
import sys
import time
import networkx as nx

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def create_test_graph(num_nodes=50):
    """Create a test graph for edge manipulation testing."""
    G = nx.erdos_renyi_graph(num_nodes, 0.3, seed=42)
    
    # Add weights to edges
    for u, v in G.edges():
        G[u][v]['weight'] = abs(hash((u, v))) % 10 + 1
    
    return G

def test_edge_error_fixes():
    """Test that edge manipulation errors are fixed."""
    print("🧪 Testing Edge Error Fixes")
    print("=" * 50)
    
    try:
        from app.algorithms import (
            greedy_spanning_tree, 
            adaptive_neighborhood_local_search,
            _evaluate_single_edge_swap,
            generate_neighbor_tree
        )
        
        # Create test graph
        G = create_test_graph(30)
        print(f"   Created test graph with {len(G.nodes())} nodes and {len(G.edges())} edges")
        
        # Test greedy algorithm
        print("\n📊 Testing Greedy Spanning Tree...")
        greedy_tree, greedy_cost = greedy_spanning_tree(G, max_children=3, penalty=100)
        print(f"   Greedy tree: {len(greedy_tree.nodes())} nodes, {len(greedy_tree.edges())} edges")
        print(f"   Greedy cost: {greedy_cost:.2f}")
        
        # Test edge swap evaluation
        print("\n📊 Testing Edge Swap Evaluation...")
        if len(greedy_tree.edges()) > 0:
            # Create a test edge swap
            tree_edges = list(greedy_tree.edges())
            graph_edges = list(G.edges())
            
            if len(tree_edges) > 0 and len(graph_edges) > len(tree_edges):
                edge_to_remove = tree_edges[0]
                # Find an edge not in the tree
                edge_to_add = None
                for edge in graph_edges:
                    if not greedy_tree.has_edge(*edge):
                        edge_to_add = edge
                        break
                
                if edge_to_add:
                    result = _evaluate_single_edge_swap(
                        G, greedy_tree.copy(), 
                        (edge_to_remove, edge_to_add), 
                        max_children=3, penalty=100
                    )
                    if result:
                        print(f"   ✅ Edge swap successful: {edge_to_remove} -> {edge_to_add}")
                    else:
                        print(f"   ⚠️ Edge swap not beneficial: {edge_to_remove} -> {edge_to_add}")
                else:
                    print("   ℹ️ No suitable edge for swap test found")
        
        # Test neighbor generation
        print("\n📊 Testing Neighbor Generation...")
        neighbor_tree = generate_neighbor_tree(G, greedy_tree.copy(), max_children=3, penalty=100)
        print(f"   Neighbor tree: {len(neighbor_tree.nodes())} nodes, {len(neighbor_tree.edges())} edges")
        
        # Test local search (limited iterations)
        print("\n📊 Testing Local Search (limited iterations)...")
        local_tree, cost_calls, score_history = adaptive_neighborhood_local_search(
            G, greedy_tree, max_children=3, penalty=100, max_iterations=50
        )
        print(f"   Local search completed: {cost_calls} cost calls")
        print(f"   Final tree: {len(local_tree.nodes())} nodes, {len(local_tree.edges())} edges")
        
        print("\n✅ All edge manipulation tests passed without errors!")
        return True
        
    except Exception as e:
        print(f"❌ Error during edge testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_optimizations():
    """Test performance optimizations."""
    print("\n🧪 Testing Performance Optimizations")
    print("=" * 50)
    
    try:
        from app.algorithms import calculate_cost_base, greedy_spanning_tree
        
        # Create larger test graph
        G = create_test_graph(100)
        print(f"   Created larger test graph with {len(G.nodes())} nodes")
        
        # Test optimized cost calculation
        print("\n📊 Testing Optimized Cost Calculation...")
        greedy_tree, _ = greedy_spanning_tree(G, max_children=4, penalty=100)
        
        # Time the cost calculation
        start_time = time.time()
        for _ in range(100):  # Run 100 times to measure performance
            cost = calculate_cost_base(greedy_tree, max_children=4, penalty=100, counter=[0])
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 100 * 1000  # Convert to milliseconds
        print(f"   Average cost calculation time: {avg_time:.3f} ms")
        print(f"   Final cost: {cost:.2f}")
        
        if avg_time < 10:  # Should be under 10ms for reasonable performance
            print("   ✅ Cost calculation performance is good")
        else:
            print("   ⚠️ Cost calculation might be slow")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during performance testing: {e}")
        return False

def test_gpu_removal():
    """Test that GPU code has been completely removed."""
    print("\n🧪 Testing GPU Code Removal")
    print("=" * 50)
    
    try:
        from app.algorithms import TORCH_AVAILABLE, CUDA_AVAILABLE, GPU_TYPE
        
        # Verify GPU variables are disabled
        print(f"   TORCH_AVAILABLE: {TORCH_AVAILABLE}")
        print(f"   CUDA_AVAILABLE: {CUDA_AVAILABLE}")
        print(f"   GPU_TYPE: {GPU_TYPE}")
        
        if not TORCH_AVAILABLE and not CUDA_AVAILABLE and GPU_TYPE is None:
            print("   ✅ GPU acceleration properly disabled")
            return True
        else:
            print("   ❌ GPU acceleration not properly disabled")
            return False
        
    except Exception as e:
        print(f"❌ Error during GPU removal test: {e}")
        return False

def test_system_stability():
    """Test system stability functions."""
    print("\n🧪 Testing System Stability Functions")
    print("=" * 50)
    
    try:
        from app.algorithms import (
            check_system_stability,
            calculate_optimal_workers,
            adaptive_worker_adjustment
        )
        
        # Test stability check
        is_stable, message = check_system_stability()
        print(f"   System stable: {is_stable}")
        print(f"   Message: {message}")
        
        # Test worker calculation
        workers = calculate_optimal_workers()
        print(f"   Optimal workers: {workers}")
        
        # Verify hard limits
        if workers <= 3:
            print("   ✅ Worker hard limit respected")
        else:
            print(f"   ❌ Worker hard limit violated: {workers} > 3")
            return False
        
        # Test worker adjustment
        adjusted = adaptive_worker_adjustment(workers, cpu_threshold=75.0)
        print(f"   Adjusted workers: {adjusted}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during stability testing: {e}")
        return False

def main():
    """Run all tests."""
    print("🔧 DCST Tool - Edge Fixes and Performance Tests")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        test_edge_error_fixes,
        test_performance_optimizations,
        test_gpu_removal,
        test_system_stability
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    end_time = time.time()
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    print(f"⏱️  Total time: {end_time - start_time:.2f} seconds")
    
    if passed == total:
        print("🎉 All tests passed!")
        print("\n📋 Summary of fixes verified:")
        print("   ✅ Edge manipulation errors fixed")
        print("   ✅ Performance optimizations working")
        print("   ✅ GPU code completely removed")
        print("   ✅ System stability measures active")
        print("\n🚀 The DCST Tool should now run faster and more reliably!")
        return True
    else:
        print("⚠️  Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
