#!/usr/bin/env python3
"""
Test script to verify the safe CPU optimization implementation.
This script tests the new adaptive resource management without running the full DCST algorithms.
"""

import os
import sys
import time

# Add the project directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_safe_cpu_configuration():
    """Test the safe CPU configuration function."""
    print("🧪 Testing Safe CPU Configuration")
    print("=" * 50)
    
    try:
        # Import the safe CPU configuration function
        from run import configure_cpu_optimization
        
        print("✅ Successfully imported configure_cpu_optimization")
        
        # Test with default safety margin
        print("\n📊 Testing with default safety margin (75%)...")
        safe_cores = configure_cpu_optimization()
        print(f"   Configured cores: {safe_cores}")
        
        # Test with conservative safety margin
        print("\n📊 Testing with conservative safety margin (50%)...")
        conservative_cores = configure_cpu_optimization(safety_margin=0.5)
        print(f"   Configured cores: {conservative_cores}")
        
        # Test with aggressive safety margin (should still be safe)
        print("\n📊 Testing with higher safety margin (90%)...")
        aggressive_cores = configure_cpu_optimization(safety_margin=0.9)
        print(f"   Configured cores: {aggressive_cores}")
        
        # Verify environment variables are set
        print("\n🔍 Checking environment variables...")
        env_vars = ["OMP_NUM_THREADS", "MKL_NUM_THREADS", "NUMEXPR_NUM_THREADS", "OPENBLAS_NUM_THREADS"]
        for var in env_vars:
            value = os.environ.get(var, "NOT SET")
            print(f"   {var}: {value}")
        
        print("\n✅ Safe CPU configuration test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during CPU configuration test: {e}")
        return False

def test_resource_monitoring():
    """Test the resource monitoring functions."""
    print("\n🧪 Testing Resource Monitoring Functions")
    print("=" * 50)
    
    try:
        # Import resource monitoring functions
        from app.algorithms import (
            detect_system_resources, 
            calculate_optimal_workers, 
            check_system_stability,
            adaptive_timeout_calculation
        )
        
        print("✅ Successfully imported resource monitoring functions")
        
        # Test system resource detection
        print("\n📊 Testing system resource detection...")
        cpu_cores, total_ram_gb, available_ram_gb = detect_system_resources()
        print(f"   CPU cores: {cpu_cores}")
        print(f"   Total RAM: {total_ram_gb:.1f} GB")
        print(f"   Available RAM: {available_ram_gb:.1f} GB")
        
        # Test optimal worker calculation
        print("\n📊 Testing optimal worker calculation...")
        optimal_workers = calculate_optimal_workers()
        print(f"   Optimal workers: {optimal_workers}")
        
        # Test system stability check
        print("\n📊 Testing system stability check...")
        is_stable, message = check_system_stability()
        print(f"   System stable: {is_stable}")
        print(f"   Message: {message}")
        
        # Test adaptive timeout calculation
        print("\n📊 Testing adaptive timeout calculation...")
        for graph_size in [10, 100, 1000, 5000]:
            timeout = adaptive_timeout_calculation(graph_size)
            print(f"   Graph size {graph_size}: {timeout}s timeout")
        
        print("\n✅ Resource monitoring test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during resource monitoring test: {e}")
        return False

def test_gpu_detection():
    """Test GPU detection and acceleration capabilities."""
    print("\n🧪 Testing GPU Detection")
    print("=" * 50)
    
    try:
        # Import GPU-related variables
        from app.algorithms import TORCH_AVAILABLE, CUDA_AVAILABLE, CUPY_AVAILABLE, GPU_TYPE, log_gpu_status
        
        print("✅ Successfully imported GPU detection variables")
        
        print(f"\n📊 GPU Status:")
        print(f"   PyTorch available: {TORCH_AVAILABLE}")
        print(f"   CUDA available: {CUDA_AVAILABLE}")
        print(f"   CuPy available: {CUPY_AVAILABLE}")
        print(f"   GPU type: {GPU_TYPE}")
        
        # Test GPU status logging
        print("\n📊 Testing GPU status logging...")
        log_gpu_status()
        
        print("\n✅ GPU detection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during GPU detection test: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Starting Safe Parallelization Tests")
    print("=" * 60)
    
    start_time = time.time()
    
    # Run all tests
    tests = [
        test_safe_cpu_configuration,
        test_resource_monitoring,
        test_gpu_detection
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    end_time = time.time()
    
    print("\n" + "=" * 60)
    print(f"🏁 Test Results: {passed}/{total} tests passed")
    print(f"⏱️  Total time: {end_time - start_time:.2f} seconds")
    
    if passed == total:
        print("🎉 All tests passed! The safe parallelization implementation is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
