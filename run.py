# run.py - Avvio semplice e pulito dell'app GUI

from app.gui import App
import tkinter as tk
from tkinter import ttk
import os
import multiprocessing

# Configure CPU optimization settings for maximum performance
def configure_cpu_optimization():
    """Configure environment variables for maximum CPU utilization."""
    cpu_count = multiprocessing.cpu_count()

    # Set environment variables for scientific libraries to use all CPU cores
    os.environ['OMP_NUM_THREADS'] = str(cpu_count)
    os.environ['MKL_NUM_THREADS'] = str(cpu_count)
    os.environ['NUMEXPR_NUM_THREADS'] = str(cpu_count)
    os.environ['OPENBLAS_NUM_THREADS'] = str(cpu_count)

    # Configure NumPy to use all available cores
    try:
        import numpy as np
        # This will be applied when numpy operations are used
        print(f"CPU optimization configured: {cpu_count} cores available")
    except ImportError:
        pass

    return cpu_count

if __name__ == "__main__":
    # Configure CPU optimization before starting the application
    cpu_count = configure_cpu_optimization()

    root = tk.Tk()
    root.title(f"DCST Tool - {cpu_count} CPU cores")

    # Imposta icona se disponibile
    try:
        icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
        if os.path.exists(icon_path):
            try:
                root.iconbitmap(default=icon_path)
            except:
                pass  # Evita crash se l'icona non è compatibile
    except NameError:
        # Handle case when __file__ is not defined
        icon_path = "icon.ico"
        if os.path.exists(icon_path):
            try:
                root.iconbitmap(default=icon_path)
            except:
                pass

    # Crea barra di progresso e avvia l'app
    progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
    app = App(root, progress_bar)

    root.mainloop()
