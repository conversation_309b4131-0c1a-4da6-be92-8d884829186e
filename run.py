# run.py - Avvio semplice e pulito dell'app GUI

from app.gui import App
import tkinter as tk
from tkinter import ttk
import os
import multiprocessing

# Safe and adaptive CPU optimization to prevent system crashes
def configure_cpu_optimization(safety_margin=0.75):
    """
    Configure environment variables for safe CPU utilization with adaptive resource management.
    Uses only a fraction of available cores and monitors system resources to prevent crashes.

    Args:
        safety_margin (float): Safety margin for resource usage (0.0-1.0)

    Returns:
        int: Number of cores configured for use
    """
    try:
        # Import psutil for system monitoring
        try:
            import psutil
        except ImportError:
            print("⚠️ psutil not available. Using conservative CPU settings.")
            # Fallback to very conservative settings
            safe_cores = max(1, multiprocessing.cpu_count() // 2)
            os.environ["OMP_NUM_THREADS"] = str(safe_cores)
            os.environ["MKL_NUM_THREADS"] = str(safe_cores)
            os.environ["NUMEXPR_NUM_THREADS"] = str(safe_cores)
            os.environ["OPENBLAS_NUM_THREADS"] = str(safe_cores)
            print(f"✅ Conservative CPU configuration: {safe_cores} cores (fallback mode)")
            return safe_cores

        cpu_count = multiprocessing.cpu_count()

        # Monitor current system load and available memory
        try:
            cpu_load = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            available_ram_gb = memory.available / (1024**3)
        except Exception:
            # Fallback if monitoring fails
            cpu_load = 50.0
            available_ram_gb = 2.0

        # Calculate safe number of cores based on system state
        safe_cores = max(1, int(cpu_count * safety_margin))

        # Apply additional safety checks
        if available_ram_gb < 2.0:
            safe_cores = 1
            print("⚠️ Low memory detected: limiting to single-core operation")
        elif cpu_load > 90.0:
            safe_cores = max(1, safe_cores // 2)
            print(f"⚠️ High CPU load detected ({cpu_load:.1f}%): reducing core usage")
        elif available_ram_gb < 4.0:
            safe_cores = max(1, min(safe_cores, 2))
            print("⚠️ Limited memory: restricting to maximum 2 cores")

        # Set environment variables for scientific libraries with safe limits
        os.environ["OMP_NUM_THREADS"] = str(safe_cores)
        os.environ["MKL_NUM_THREADS"] = str(safe_cores)
        os.environ["NUMEXPR_NUM_THREADS"] = str(safe_cores)
        os.environ["OPENBLAS_NUM_THREADS"] = str(safe_cores)

        # Additional safety settings to prevent library conflicts
        os.environ["NUMBA_NUM_THREADS"] = str(safe_cores)
        os.environ["VECLIB_MAXIMUM_THREADS"] = str(safe_cores)

        # Configure NumPy if available
        try:
            import numpy as np
            print(f"✅ Safe CPU optimization configured: {safe_cores}/{cpu_count} cores")
            print(f"   System status: {cpu_load:.1f}% CPU load, {available_ram_gb:.1f}GB available RAM")
        except ImportError:
            print(f"✅ Safe CPU optimization configured: {safe_cores}/{cpu_count} cores (NumPy not available)")

        return safe_cores

    except Exception as e:
        print(f"❌ Error in CPU configuration: {e}")
        # Emergency fallback to single core
        safe_cores = 1
        os.environ["OMP_NUM_THREADS"] = "1"
        os.environ["MKL_NUM_THREADS"] = "1"
        os.environ["NUMEXPR_NUM_THREADS"] = "1"
        os.environ["OPENBLAS_NUM_THREADS"] = "1"
        print(f"🛑 Emergency fallback: using single core due to configuration error")
        return safe_cores

if __name__ == "__main__":
    # Configure CPU optimization before starting the application
    cpu_count = configure_cpu_optimization()

    root = tk.Tk()
    root.title(f"DCST Tool - {cpu_count} CPU cores")

    # Imposta icona se disponibile
    try:
        icon_path = os.path.join(os.path.dirname(__file__), "icon.ico")
        if os.path.exists(icon_path):
            try:
                root.iconbitmap(default=icon_path)
            except:
                pass  # Evita crash se l'icona non è compatibile
    except NameError:
        # Handle case when __file__ is not defined
        icon_path = "icon.ico"
        if os.path.exists(icon_path):
            try:
                root.iconbitmap(default=icon_path)
            except:
                pass

    # Crea barra di progresso e avvia l'app
    progress_bar = ttk.Progressbar(root, orient="horizontal", length=300, mode="determinate")
    app = App(root, progress_bar)

    root.mainloop()
