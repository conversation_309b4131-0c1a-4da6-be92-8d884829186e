#!/usr/bin/env python3
"""
Complete workflow test for DCST Tool with adaptive resource management.
This test simulates a full optimization run without GUI.
"""

import sys
import os
import time
import logging

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_complete_workflow():
    """Test the complete DCST optimization workflow."""
    print("🧪 DCST TOOL - COMPLETE WORKFLOW TEST")
    print("=" * 60)
    
    try:
        # Import modules
        print("📦 Importing modules...")
        from algorithms import (
            test_instance,
            detect_system_resources,
            calculate_optimal_workers,
            log_gpu_status
        )
        from utils import generate_connected_random_graph
        print("✅ All modules imported successfully")
        
        # Initialize adaptive resource management
        print("\n🔧 Initializing adaptive resource management...")
        cpu_cores, total_ram_gb, available_ram_gb = detect_system_resources()
        optimal_workers = calculate_optimal_workers(safety_margin=0.7)
        log_gpu_status()
        
        print(f"✅ System: {cpu_cores} cores, {total_ram_gb:.1f}GB RAM")
        print(f"✅ Optimal workers: {optimal_workers}")
        
        # Test small graph
        print("\n🌐 Testing small graph (30 nodes)...")
        G_small = generate_connected_random_graph(30, 0.3)
        print(f"✅ Generated graph: {len(G_small.nodes())} nodes, {len(G_small.edges())} edges")
        
        start_time = time.time()
        results_small = test_instance(
            G_small, 
            max_children=3, 
            penalty=100, 
            instance_name="Small Test"
        )
        end_time = time.time()
        
        print(f"✅ Small graph optimization completed in {end_time - start_time:.2f}s")
        print(f"   Greedy cost: {results_small.get('greedy_cost', 'N/A')}")
        print(f"   Local search cost: {results_small.get('local_cost', 'N/A')}")
        print(f"   SA cost: {results_small.get('sa_cost', 'N/A')}")
        
        # Test medium graph
        print("\n🌐 Testing medium graph (100 nodes)...")
        G_medium = generate_connected_random_graph(100, 0.2)
        print(f"✅ Generated graph: {len(G_medium.nodes())} nodes, {len(G_medium.edges())} edges")
        
        start_time = time.time()
        results_medium = test_instance(
            G_medium, 
            max_children=3, 
            penalty=100, 
            instance_name="Medium Test"
        )
        end_time = time.time()
        
        print(f"✅ Medium graph optimization completed in {end_time - start_time:.2f}s")
        print(f"   Greedy cost: {results_medium.get('greedy_cost', 'N/A')}")
        print(f"   Local search cost: {results_medium.get('local_cost', 'N/A')}")
        print(f"   SA cost: {results_medium.get('sa_cost', 'N/A')}")
        
        # Verify adaptive features were used
        print("\n🔍 Verifying adaptive features...")
        if 'system_resources' in results_medium:
            resources = results_medium['system_resources']
            print(f"✅ Resource management active:")
            print(f"   CPU cores detected: {resources['cpu_cores']}")
            print(f"   RAM detected: {resources['total_ram_gb']:.1f}GB")
            print(f"   Optimal workers: {resources['optimal_workers']}")
            print(f"   GPU available: {resources['gpu_available']}")
        
        # Performance summary
        print("\n📊 PERFORMANCE SUMMARY")
        print("=" * 60)
        
        # Small graph summary
        if results_small:
            print(f"Small Graph (30 nodes):")
            print(f"  Best algorithm: {get_best_algorithm(results_small)}")
            print(f"  Best cost: {get_best_cost(results_small)}")
            print(f"  Total time: {get_total_time(results_small):.2f}s")
        
        # Medium graph summary
        if results_medium:
            print(f"Medium Graph (100 nodes):")
            print(f"  Best algorithm: {get_best_algorithm(results_medium)}")
            print(f"  Best cost: {get_best_cost(results_medium)}")
            print(f"  Total time: {get_total_time(results_medium):.2f}s")
        
        print("\n🎉 WORKFLOW TEST COMPLETED SUCCESSFULLY")
        print("✅ All adaptive resource management features working")
        print("✅ System stability maintained throughout testing")
        print("✅ Performance optimization active")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Workflow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def get_best_algorithm(results):
    """Get the name of the best performing algorithm."""
    costs = {}
    if 'greedy_cost' in results:
        costs['Greedy'] = results['greedy_cost']
    if 'local_cost' in results:
        costs['Local Search'] = results['local_cost']
    if 'sa_cost' in results:
        costs['Simulated Annealing'] = results['sa_cost']
    
    if costs:
        return min(costs, key=costs.get)
    return "Unknown"

def get_best_cost(results):
    """Get the best cost achieved."""
    costs = []
    if 'greedy_cost' in results:
        costs.append(results['greedy_cost'])
    if 'local_cost' in results:
        costs.append(results['local_cost'])
    if 'sa_cost' in results:
        costs.append(results['sa_cost'])
    
    return min(costs) if costs else "N/A"

def get_total_time(results):
    """Get the total execution time."""
    total_time = 0
    if 'greedy_time' in results:
        total_time += results['greedy_time']
    if 'local_time' in results:
        total_time += results['local_time']
    if 'sa_time' in results:
        total_time += results['sa_time']
    
    return total_time

if __name__ == "__main__":
    success = test_complete_workflow()
    sys.exit(0 if success else 1)
